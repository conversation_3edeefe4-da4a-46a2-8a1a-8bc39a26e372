using MediatR;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.CustomExceptions;
using RJO.BuildingBlocks.EventBus.Abstractions;
using RJO.BuildingBlocks.WebCommon.Authorization;
using RJO.IdentityService.Domain;
using RJO.IdentityService.Domain.Tenants;
using RJO.IdentityService.Persistence.DataBase;
using RJO.IdentityService.Services.DTO.Tenant;
using RJO.IdentityService.Services.Handlers.Role;
using RJO.IdentityService.Services.Helpers;
using RJO.IdentityService.Services.Services;
using RJO.IntegrationEvents.Commons.Events;
using RJO.MultiTenancyServer.Core.Stores;

namespace RJO.IdentityService.Services.Handlers.Tenant;

public class CreateTenantCommand : IRequest<CreateTenantResponse>
{
	public CreateTenantRequest Tenant { get; init; }
}

public class CreateTenantCommandHandler(
	ApplicationDbContext dbContext,
	ITenantStore<ApplicationTenant> tenantStore,
	IEventBus eventBus,
	GraphService graphService) 
	: IRequestHandler<CreateTenantCommand, CreateTenantResponse>
{
	const int DefaultPasswordLength = 12;
	
	public async Task<CreateTenantResponse> Handle(CreateTenantCommand request, CancellationToken cancellationToken)
	{
		var response = new CreateTenantResponse();
		var tenant = await CreateTenantAsync(request, cancellationToken);
		response.TenantId = tenant.Id;
		var roles = await CreateRolesAsync();

		var role = roles.First(a => a.Name == RoleNames.SecurityRole);
		var adminRole = roles.First(a => a.Name == RoleNames.AdminRole);
		
		foreach (var employee in request.Tenant.Employees)
		{
			response.Employees.Add(await CreateEmployeeAsync(employee, tenant.Id, cancellationToken));
		}

		await dbContext.SaveChangesAsync(cancellationToken);

		foreach (var employee in request.Tenant.Employees)
		{
			await PublishEmployeeEventAsync(employee, employee.IsTenantAdmin ? role.Id : adminRole.Id, tenant.Id);
		}
		return response;
	}

	async Task<ApplicationTenant> CreateTenantAsync(CreateTenantCommand request, CancellationToken cancellationToken)
	{
		var existingTenant = await dbContext.Tenants.FirstOrDefaultAsync(a => a.DisplayName == request.Tenant.TenantName, cancellationToken);
		AssertionConcern.ArgumentIsNull(existingTenant, "The tenant already exists");
		var tenant = new ApplicationTenant();
		tenant.DisplayName = request.Tenant.TenantName;
		tenant.CanonicalName = request.Tenant.TenantName.Replace(" ", "", StringComparison.InvariantCultureIgnoreCase);
		tenant.NormalizedCanonicalName = tenant.CanonicalName;
		var tenancyResult = await tenantStore.CreateAsync(tenant, cancellationToken);
		AssertionConcern.ArgumentIsTrue(tenancyResult.Succeeded, $"Tenant creation failed: {tenancyResult.Errors.FirstOrDefault()?.Description ?? ""}");
		dbContext.TenancyContext.Tenant = tenant;

		return tenant;
	}

	async Task<ApplicationRole[]> CreateRolesAsync()
	{
		ApplicationRole[] roles =
			[
				new ()
				{
					Name = RoleNames.SecurityRole,
					Id = IdentityGenerator.NewSequentialGuid(),
					NormalizedName = RoleNames.SecurityRole.ToUpperInvariant()
				},
				new ()
				{
					Name = RoleNames.AdminRole,
					Id = IdentityGenerator.NewSequentialGuid(),
					NormalizedName = RoleNames.AdminRole.ToUpperInvariant()
				},
			];

		await dbContext.Roles.AddRangeAsync(roles);
		var role = roles.First(a => a.Name == RoleNames.SecurityRole);
		
		ApplicationRoleClaim[] applicationRoleClaims =
		[
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractActivateDNH),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractHTACreate),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractHTAEditQuantity),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractHTAEditNonQuantity),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractBasisCreate),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractBasisEditNonQuantity),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractBasisEditQuantity),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractFlatPriceCreate),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractFlatPriceEditQuantity),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ContractFlatPriceEditNonQuantity),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.OfferHTACreate),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.OfferHTAEdit),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.OfferBasisCreate),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.OfferBasisEdit),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.OfferFlatPriceCreate),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.OfferFlatPriceEdit),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.LiveLedgerView),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.ReviewAndReleaseView),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.SettingsAdmin),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.UseCashSettlement),
			new (role.Id, CustomClaimTypes.Grant, OperationsConstants.MobileAppEnable),
			new (role.Id, CustomClaimTypes.Grant, CustomClaimValues.SecurityClaim)
		];
		dbContext.RoleClaims.AddRange(applicationRoleClaims);

		return roles;
	}

	async Task<EmployeeResponse> CreateEmployeeAsync(CreateEmployeeRequest employee, Guid tenantId, CancellationToken cancellationToken)
	{
		var username = $"{employee.FirstName}.{employee.LastName}".ToLowerInvariant();
		var password = PasswordGenerator.GeneratePassword(DefaultPasswordLength);
		var userPrincipalName = $"{username}@hrvysthedge.onmicrosoft.com";
		var graphUser = await graphService.CreateMemberUserAsync(username, userPrincipalName, password, employee.FirstName, employee.LastName, cancellationToken);
		var user = new ApplicationUser(username, employee.FirstName, employee.LastName, employee.UserEmail, tenantId);
		user.NormalizedEmail = employee.UserEmail;
		user.NormalizedUserName = username;
		user.GraphId = Guid.Parse(graphUser.Id);
		dbContext.Users.Add(user);

		return new()
		{
			UserPrincipal = userPrincipalName,
			Password = password,
			GraphId = graphUser.Id
		};
	}

	async Task PublishEmployeeEventAsync(CreateEmployeeRequest employee, Guid roleId, Guid tenantId)
	{
		var username = $"{employee.FirstName}.{employee.LastName}".ToLowerInvariant();

		var employeeCreateEvent = new EmployeeCreatedEvent
		{
			Number = employee.Number,
			UserName = username,
			FirstName = employee.FirstName,
			LastName = employee.LastName,
			Email = employee.UserEmail,
			RoleId = roleId,
			TenantId = tenantId,
			Tag50 = false,
			Tag50Account = null
		};

		await eventBus.Publish(employeeCreateEvent);
	}
}
