using IdentityServer4.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using RJO.IdentityService.WebApi.Configuration;

namespace RJO.IdentityService.WebApi.Services;

/// <summary>
/// Service for validating return URLs to prevent open redirect attacks
/// </summary>
public class ReturnUrlValidationService : IReturnUrlValidationService
{
    private readonly IIdentityServerInteractionService _interaction;
    private readonly IUrlHelper _urlHelper;
    private readonly ILogger<ReturnUrlValidationService> _logger;
    private readonly ReturnUrlValidationOptions _options;

    public ReturnUrlValidationService(
        IIdentityServerInteractionService interaction,
        IUrlHelper urlHelper,
        ILogger<ReturnUrlValidationService> logger,
        IOptions<ReturnUrlValidationOptions> options)
    {
        _interaction = interaction;
        _urlHelper = urlHelper;
        _logger = logger;
        _options = options.Value;
    }

    public async Task<bool> IsValidReturnUrlAsync(string returnUrl)
    {
        if (string.IsNullOrWhiteSpace(returnUrl))
        {
            _logger.LogWarning("Return URL validation failed: URL is null or empty");
            return false;
        }

        try
        {
            // First check if it's a local URL
            if (_urlHelper.IsLocalUrl(returnUrl))
            {
                _logger.LogDebug("Return URL is local: {ReturnUrl}", returnUrl);
                return true;
            }

            // Check if IdentityServer considers it valid
            if (_interaction.IsValidReturnUrl(returnUrl))
            {
                _logger.LogDebug("Return URL is valid per IdentityServer: {ReturnUrl}", returnUrl);
                return true;
            }

            // Check against whitelist
            if (IsWhitelistedUrl(returnUrl))
            {
                _logger.LogDebug("Return URL is whitelisted: {ReturnUrl}", returnUrl);
                return true;
            }

            _logger.LogWarning("Return URL validation failed: {ReturnUrl}", returnUrl);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating return URL: {ReturnUrl}", returnUrl);
            return false;
        }
    }

    public async Task<string> GetSafeReturnUrlAsync(string returnUrl)
    {
        if (await IsValidReturnUrlAsync(returnUrl))
        {
            return returnUrl;
        }

        // Return default fallback URL
        var fallbackUrl = _options.DefaultFallbackUrl ?? "/";
        _logger.LogWarning("Using fallback URL {FallbackUrl} instead of invalid return URL {ReturnUrl}", 
            fallbackUrl, returnUrl);
        
        return fallbackUrl;
    }

    public string ResolveReturnUrlCode(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
        {
            return null;
        }

        if (_options.PredefinedUrls?.TryGetValue(code, out var url) == true)
        {
            _logger.LogDebug("Resolved return URL code {Code} to {Url}", code, url);
            return url;
        }

        _logger.LogWarning("Unknown return URL code: {Code}", code);
        return null;
    }

    private bool IsWhitelistedUrl(string returnUrl)
    {
        if (_options.AllowedDomains == null || !_options.AllowedDomains.Any())
        {
            return false;
        }

        try
        {
            var uri = new Uri(returnUrl, UriKind.Absolute);
            var host = uri.Host.ToLowerInvariant();

            // Check exact domain matches
            if (_options.AllowedDomains.Contains(host, StringComparer.OrdinalIgnoreCase))
            {
                return true;
            }

            // Check subdomain matches (if enabled)
            if (_options.AllowSubdomains)
            {
                foreach (var allowedDomain in _options.AllowedDomains)
                {
                    if (host.EndsWith($".{allowedDomain}", StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }
                }
            }

            return false;
        }
        catch (UriFormatException)
        {
            _logger.LogWarning("Invalid URI format for return URL: {ReturnUrl}", returnUrl);
            return false;
        }
    }
}
