namespace RJO.IdentityService.WebApi.Services;

/// <summary>
/// Service for validating return URLs to prevent open redirect attacks
/// </summary>
public interface IReturnUrlValidationService
{
    /// <summary>
    /// Validates if the return URL is safe to redirect to
    /// </summary>
    /// <param name="returnUrl">The return URL to validate</param>
    /// <returns>True if the URL is valid and safe, false otherwise</returns>
    Task<bool> IsValidReturnUrlAsync(string returnUrl);

    /// <summary>
    /// Gets a safe return URL, either the original if valid or a default fallback
    /// </summary>
    /// <param name="returnUrl">The return URL to validate</param>
    /// <returns>A safe return URL</returns>
    Task<string> GetSafeReturnUrlAsync(string returnUrl);

    /// <summary>
    /// Resolves a return URL code/identifier to the actual URL
    /// </summary>
    /// <param name="code">The code/identifier for the return URL</param>
    /// <returns>The resolved URL or null if code is invalid</returns>
    string ResolveReturnUrlCode(string code);
}
