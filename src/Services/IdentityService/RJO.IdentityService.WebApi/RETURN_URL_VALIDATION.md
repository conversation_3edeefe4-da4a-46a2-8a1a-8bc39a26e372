# Return URL Validation Implementation

## Overview

This document describes the implementation of return URL validation to prevent open redirect attacks in the HrvystEdge Identity Service.

## Security Issue

The login endpoint previously accepted a `returnUrl` parameter without validation, allowing attackers to redirect users to external malicious domains after successful OAuth authentication.

## Solution

### 1. Return URL Validation Service

**Files:**
- `Services/IReturnUrlValidationService.cs` - Interface
- `Services/ReturnUrlValidationService.cs` - Implementation
- `Configuration/ReturnUrlValidationOptions.cs` - Configuration model

**Features:**
- **Local URL Validation**: Validates URLs starting with `/` or `~/`
- **IdentityServer4 Integration**: Uses `IIdentityServerInteractionService.IsValidReturnUrl()`
- **Domain Whitelist**: Configurable list of allowed domains per environment
- **Code/Identifier Mapping**: Predefined URL codes for secure redirects
- **Fallback URLs**: Safe default URLs when validation fails

### 2. Configuration

**Environment-specific settings in appsettings files:**

```json
{
  "ReturnUrlValidation": {
    "AllowedDomains": [
      "hedge.hrvyst.com",
      "pr-eastus-identity-app.azurewebsites.net"
    ],
    "AllowSubdomains": false,
    "DefaultFallbackUrl": "https://hedge.hrvyst.com/",
    "PredefinedUrls": {
      "main": "https://hedge.hrvyst.com/",
      "admin": "https://pr-eastus-identity-app.azurewebsites.net/authorized"
    }
  }
}
```

### 3. Controller Updates

**AccountController.cs changes:**
- Added `IReturnUrlValidationService` dependency
- Enhanced `Login` method to support URL codes
- Updated `ExternalLoginCallback` to validate return URLs
- Automatic fallback to safe URLs when validation fails

## Usage

### Direct URL
```
GET /api/account/login?returnUrl=https://hedge.hrvyst.com/dashboard
```

### Using Codes
```
GET /api/account/login?code=main
GET /api/account/login?code=admin
```

## Validation Logic

The service validates URLs in this order:

1. **Local URLs**: `/path` or `~/path` (always allowed)
2. **IdentityServer4**: Uses built-in validation for OAuth flows
3. **Domain Whitelist**: Checks against configured allowed domains
4. **Fallback**: Returns safe default URL if validation fails

## Security Benefits

- **Prevents Open Redirects**: Blocks malicious external redirects
- **Environment-specific**: Different whitelists per environment
- **Logging**: Comprehensive security event logging
- **Graceful Degradation**: Falls back to safe URLs instead of failing
- **Code-based URLs**: Reduces URL tampering risks

## Testing

Unit tests are provided in:
- `RJO.IdentityService.WebApi.Tests/Services/ReturnUrlValidationServiceTests.cs`

Tests cover:
- Local URL validation
- Malicious URL detection
- Domain whitelist validation
- Code resolution
- Fallback behavior

## Environment Configuration

### Development/Local
- Allows localhost and development domains
- Includes local development URLs

### Staging/QA
- Staging environment domains only
- Test-specific redirect URLs

### Production
- Production domains only (`hedge.hrvyst.com`)
- Secure production URLs

## Migration Notes

- Existing functionality preserved
- Backward compatible with current OAuth flows
- No breaking changes to API contracts
- Enhanced security logging added

## Monitoring

The service logs the following security events:
- Invalid return URL attempts
- Fallback URL usage
- Code resolution failures
- Validation errors

Monitor these logs for potential security threats.
