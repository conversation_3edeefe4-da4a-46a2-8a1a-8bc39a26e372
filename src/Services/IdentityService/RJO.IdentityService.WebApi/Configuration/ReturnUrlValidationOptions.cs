namespace RJO.IdentityService.WebApi.Configuration;

/// <summary>
/// Configuration options for return URL validation
/// </summary>
public class ReturnUrlValidationOptions
{
    public const string SectionName = "ReturnUrlValidation";

    /// <summary>
    /// List of allowed domains for return URLs
    /// </summary>
    public List<string> AllowedDomains { get; set; } = new();

    /// <summary>
    /// Whether to allow subdomains of the allowed domains
    /// </summary>
    public bool AllowSubdomains { get; set; } = false;

    /// <summary>
    /// Default fallback URL when return URL is invalid
    /// </summary>
    public string DefaultFallbackUrl { get; set; } = "/";

    /// <summary>
    /// Predefined URLs mapped by codes/identifiers
    /// </summary>
    public Dictionary<string, string> PredefinedUrls { get; set; } = new();
}
