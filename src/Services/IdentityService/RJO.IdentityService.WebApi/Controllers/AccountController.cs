using IdentityServer4;
using IdentityServer4.Configuration;
using IdentityServer4.Stores;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.CustomExceptions.SecurityExceptions;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.IdentityService.Domain;
using RJO.IdentityService.Domain.Tenants;
using RJO.IdentityService.Services.Handlers;
using RJO.IdentityService.Services.Handlers.Tenant;
using RJO.IdentityService.Services.Handlers.UserClaim;
using RJO.IdentityService.WebApi.Core;
using RJO.MultiTenancyServer.Core;
using System.Security.Claims;

namespace RJO.IdentityService.WebApi.Controllers;

[ApiController]
[Produces("application/json")]
[Route("api/account")]
[AllowAnonymous]
public class AccountController : BaseController
{
	const string DefaultClient = "reactjsclient";
	
	readonly SignInManager<ApplicationUser> _signInManager;
	readonly UserManager<ApplicationUser> _userManager;
	readonly IUserClaimsPrincipalFactory<ApplicationUser> _principalFactory;
	readonly IdentityServerOptions _identityServerOptions;
	readonly IdentityServerTools _tools;
	readonly ITenancyContext<ApplicationTenant> _tenancyContext;
	readonly IMediator _mediator;
	readonly ILogger<AccountController> _logger;
	readonly IClientStore _clientStore;

	/// <summary>Initializes a new instance of the <see cref="AccountController" /> class.</summary>
	/// <param name="mediator">The mediator.</param>
	/// <param name="signInManager">The sign in manager.</param>
	/// <param name="userManager">The user manager.</param>
	/// <param name="principalFactory">The principal factory.</param>
	/// <param name="identityServerOptions">The identity server options.</param>
	/// <param name="tools">The tools.</param>
	/// <param name="tenancyContext">The tenancy context.</param>
	/// <param name="logger">The logger.</param>
	/// <param name="clientStore">The client store.</param>
	public AccountController(IMediator mediator,
		SignInManager<ApplicationUser> signInManager,
		UserManager<ApplicationUser> userManager,
		IUserClaimsPrincipalFactory<ApplicationUser> principalFactory,
		IdentityServerOptions identityServerOptions,
		IdentityServerTools tools,
		ITenancyContext<ApplicationTenant> tenancyContext,
		ILogger<AccountController> logger,
		IClientStore clientStore)
	{
		_mediator = mediator;
		_signInManager = signInManager;
		_userManager = userManager;
		_principalFactory = principalFactory;
		_identityServerOptions = identityServerOptions;
		_tools = tools;
		_tenancyContext = tenancyContext;
		_logger = logger;
		_clientStore = clientStore;
	}

	[HttpGet("Login")]
	[AllowAnonymous]
	public Task<IActionResult> Login(string returnUrl) => ExternalLogin("oidc", returnUrl);

	/// <summary>
	/// Externals the login.
	/// </summary>
	/// <param name="provider">The provider.</param>
	/// <param name="returnUrl">The return URL.</param>
	/// <returns></returns>
	[HttpPost]
	[HttpGet]
	[AllowAnonymous]
	public Task<IActionResult> ExternalLogin(string provider, string returnUrl = null)
	{
		// Request a redirect to the external login provider.
		var redirectUrl = Url.Action("ExternalLoginCallback", "Account", new { ReturnUrl = returnUrl });
		var properties = _signInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl);
		return Task.FromResult<IActionResult>(Challenge(properties, provider));
	}

	/// <summary>
	/// Handle logout page postback
	/// <response code="200">Returned if the operation was completed</response>
	/// <response code="400">Returned if the operation fails</response>
	/// </summary>
	[HttpPost("Logout")]
	[AllowAnonymous]
	public async Task<ActionResult<Result<bool>>> Logout()
	{
		if (User.Identity is { IsAuthenticated: false })
		{
			return Result(true);
		}

		await _signInManager.SignOutAsync();
		return Result(true);
	}

	/// <summary>
	/// Externals the login callback.
	/// </summary>
	/// <param name="returnUrl">The return URL.</param>
	/// <param name="remoteError">The remote error.</param>
	/// <returns></returns>
	/// <exception cref="System.Security.SecurityException">
	/// Not info for external login was found
	/// or
	/// User email was not found
	/// or
	/// User was not found on our system
	/// or
	/// Login error. " + AzureIdentityErrorHelper.GetErrors(signInResult.Errors)
	/// </exception>
	[HttpGet("Callback")]
	[AllowAnonymous]
	public async Task<IActionResult> ExternalLoginCallback(string returnUrl = null, string remoteError = null)
	{
		//await _interaction.GetAuthorizationContextAsync(returnUrl);
		if (returnUrl == null)
		{
			throw new SecurityException("returnUrl was not provided");
		}
		
		if (!await IsValidReturnUrlAsync(returnUrl))
		{
			_logger.LogWarning("Invalid return URL detected: {ReturnUrl}. This could be a malicious redirect attempt.", returnUrl);
			throw new SecurityException("Invalid return URL");
		}

		if (remoteError != null)
		{
			throw new SecurityException(remoteError);
		}

		var info = await _signInManager.GetExternalLoginInfoAsync();
		if (info == null)
		{
			throw new SecurityException("Not info for external login was found");
		}

		var email = info.Principal.FindFirstValue("preferred_username");
		if (string.IsNullOrEmpty(email))
		{
			throw new SecurityException("User email was not found");
		}

		var userFound = await _userManager.FindByEmailAsync(email);
		if (userFound == null)
		{
			throw new SecurityException("User was not found on our system");
		}

		if (!userFound.IsActive)
			throw new SecurityException("User has been disabled on our system");

		var tenant = await _mediator.Send(new GetTenantByIdQuery { Id = userFound.TenantId });
		_tenancyContext.Tenant = tenant;
		var signInResult = await _userManager.AddLoginAsync(userFound, info);
		if (!signInResult.Succeeded && !signInResult.Errors.Any(x => x.Code == "LoginAlreadyAssociated"))
		{
			throw new SecurityException("Login error. " + AzureIdentityErrorHelper.GetErrors(signInResult.Errors));
		}

		//await _signInManager.SignInAsync(userFound, isPersistent: false);
		// Sign in the user with this external login provider if the user already has a login.
		//Microsoft.AspNetCore.Identity.SignInResult result = await _signInManager.ExternalLoginSignInAsync(info.LoginProvider, info.ProviderKey, isPersistent: false);
		//if (!result.Succeeded)
		//{
		//    throw new SecurityException("Login error");
		//}
		var claimsPrincipal = await _principalFactory.CreateAsync(userFound);
		if (claimsPrincipal == null)
		{
			throw new SecurityException("User was not found on our system");
		}

		var claims = claimsPrincipal.Claims.Where(x => x.Type != CustomClaimTypes.Grant).ToList();
		claims.Add(new(CustomClaimTypes.Tenant, tenant.NormalizedCanonicalName));
		claims.Add(new(CustomClaimTypes.TenantDisplayName, tenant.DisplayName));
		var systemFlags = await _mediator.Send(new GetSystemFlagsQuery { TenantId = tenant.Id });
		foreach (var flag in systemFlags)
		{
			claims.Add(new(CustomClaimTypes.Grant, flag));
		}

		var userClaims = await _mediator.Send(new GetAllUserClaimByUserIdQuery { Id = userFound.Id });
		foreach (var userClaim in userClaims)
		{
			claims.Add(new(CustomClaimTypes.Grant, userClaim.Name));
		}

		_logger.LogInformation("[IdSe] Access to the system: {Email}, tenant: {Tenant}, claims: {Claims}",
						email,
						tenant.NormalizedCanonicalName,
						Format(claims));
		var lifeTimeInSeconds = Convert.ToInt32((DateTime.Now.AddDays(1).Date - DateTime.Now).TotalSeconds) + 18000;
		var token = await _tools.IssueJwtAsync(
			lifeTimeInSeconds,
			claims: claims,
			issuer: _identityServerOptions.IssuerUri);
		var path = returnUrl + "?Token=" + token;
		return Redirect(path);
	}

	static string Format(List<Claim> claims) => string.Join(",", from a in claims select a.Value);

	/// <summary>
	/// Validates the return URL to prevent open redirect attacks.
	/// </summary>
	/// <param name="returnUrl">The return URL to validate.</param>
	/// <returns>True if the return URL is valid; otherwise, false.</returns>
	/// <remarks>
	/// We use a custom validation approach instead of relying on 
	/// <see cref="IIdentityServerInteractionService.IsValidReturnUrl"/> or 
	/// <see cref="IIdentityServerInteractionService.GetAuthorizationContextAsync"/>
	/// because our IdentityServer4 + Microsoft Entra ID implementation bypasses the standard OAuth flow.
	/// 
	/// In a standard OAuth2 flow:
	/// 1. The client calls /connect/authorize.
	/// 2. IdentityServer4 creates an authorization context, which its services can validate against.
	/// 
	/// In our flow:
	/// 1. The client calls /api/account/login directly.
	/// 2. No authorization context is created, so built-in validation methods don't apply.
	/// 
	/// To secure this flow, we manually check that the return URL matches one of the 
	/// redirect URIs configured for the client in the IdentityServer4 ClientRedirectUris table.
	/// </remarks>
	async Task<bool> IsValidReturnUrlAsync(string returnUrl)
	{
		if (string.IsNullOrWhiteSpace(returnUrl))
		{
			return false;
		}

		try
		{
			string[] clientIds = [DefaultClient];

			foreach (var clientId in clientIds)
			{
				var client = await _clientStore.FindClientByIdAsync(clientId);
				
				if (client == null)
				{
					_logger.LogDebug("Client {ClientId} not found", clientId);
					continue;
				}
				
				foreach (var allowedUri in client.RedirectUris)
				{
					if (string.Equals(returnUrl, allowedUri, StringComparison.OrdinalIgnoreCase))
					{
						_logger.LogDebug("Return URL matches client {ClientId} redirect URI: {ReturnUrl}", clientId, returnUrl);
						return true;
					}
				}
			}

			_logger.LogDebug("Return URL not found in any client redirect URIs: {ReturnUrl}", returnUrl);
			return false;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error validating return URL: {ReturnUrl}", returnUrl);
			return false;
		}
	}
}
