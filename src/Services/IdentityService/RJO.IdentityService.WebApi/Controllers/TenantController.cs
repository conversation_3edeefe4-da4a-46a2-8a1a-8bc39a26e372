using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RJO.BuildingBlocks.WebCommon;
using RJO.BuildingBlocks.WebCommon.Models;
using RJO.IdentityService.Services.DTO.Tenant;
using RJO.IdentityService.Services.Handlers.Tenant;

namespace RJO.IdentityService.WebApi.Controllers;

[Route("api/[controller]")]
[ApiController]
public class TenantController : BaseController
{
	readonly IMediator _mediator;

	/// <summary>
	/// Initializes a new instance of the <see cref="UserController"/> class.
	/// </summary>
	/// <param name="mediator">The mediator.</param>
	public TenantController(IMediator mediator) => _mediator = mediator;

	/// <summary>
	/// Gets the user information.
	/// </summary>
	/// <returns></returns>
	/// <response code="200">Returned if the operation was completed</response>
	/// <response code="400">Returned if the operation fails</response>
	[ProducesResponseType(StatusCodes.Status200OK)]
	[ProducesResponseType(StatusCodes.Status400BadRequest)]
	[HttpPost("create")]
	public async Task<ActionResult<Result<CreateTenantResponse>>> CreateTenant([FromBody] CreateTenantRequest request) => 
		Result(await _mediator.Send(new CreateTenantCommand { TenantRequest = request }));

}
