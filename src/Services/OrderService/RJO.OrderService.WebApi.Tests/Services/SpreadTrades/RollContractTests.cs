using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using RJO.BuildingBlocks.Common;
using RJO.BuildingBlocks.Common.Http;
using RJO.BuildingBlocks.Common.LaunchDarkly;
using RJO.BuildingBlocks.Testing;
using RJO.OrderService.Common.Resources;
using RJO.OrderService.Domain;
using RJO.OrderService.Domain.Settings;
using RJO.OrderService.Services.DTO.Contract;
using RJO.OrderService.WebApi.Testing;
using RJO.OrderService.WebApi.Tests.Services.HedgingThreshold;
using TestStack.BDDfy;
using TestStack.BDDfy.Xunit;
using Xunit;
using Xunit.Abstractions;

namespace RJO.OrderService.WebApi.Tests.Services.SpreadTrades;

public class RollContractTests(OrderService fixture, ITestOutputHelper outputHelper) : HedgingThresholdBase(fixture, outputHelper)
{
	TenantData _tenantData;
	FakeUser _fakeUser;
	Location _deliveryLocation;
	Bidsheet _bidsheet;
	Commodity _commodity;
	Region _region;
	short _cropYear;
	
	[BddfyTheory]
	[InlineData(TenantNames.MidIowa)]
	async Task Scenario1(string tenant)
	{
		_tenantData = TestData.For(tenant);
		_fakeUser = _tenantData.FakeUsers.Alice;
		_deliveryLocation = _tenantData.Locations.Oakbrook;
		_region = _tenantData.Regions.Default;
		_cropYear = TestData.CurrentYear;

		await Fixture.RequireFeatureFlag(() =>
		{
			this
				.Given(x => x.ACommodityWithLotFactorIsCreated("CornSample123", 75m, 0.05m, _tenantData.Products.Corn.Id, 5000))
				.And(x => x.ABidsheetIsConfigured(_commodity, _deliveryLocation, _cropYear))
				.When(x => x.AnHtaBuyContractIsCreated(_tenantData, _fakeUser, _commodity, _deliveryLocation, _bidsheet, _region, 4000))
				.And(x => x.TheHtaContractIsRolled(_bidsheet.FutureMonth, 4000))
				// .Then(x => x.TheRemainingBalanceShouldBe(_tenantData, _commodity, _region, _cropYear, 4000))
				.BDDfy();

			return Task.CompletedTask;
		}, FeatureFlags.EnableHedgingThreshold, ContextFactory.CreateTenantContext(_tenantData.Id.ToString(), _tenantData.Name));
	}
	
	async Task ACommodityWithLotFactorIsCreated(string name, decimal priceCtrl, decimal basisCtrl, Guid productId, int lotFactor) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var commodity = Create.Commodity(name, priceCtrl, basisCtrl, productId, lotFactor);
			dbContext.Commodities.Add(commodity);
			await dbContext.SaveChangesAsync();
			_commodity = commodity;
		});

	async Task ABidsheetIsConfigured(Commodity commodity, Location deliveryLocation, short cropYear) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var product = await dbContext.Products.FindAsync(commodity.ProductId);
			product.Should().NotBeNull();

			var bidsheet = Create.Bidsheet(commodity.Id, deliveryLocation.Id, -0.35m, product, cropYear);
			dbContext.SettingsBidsheets.Add(bidsheet);
			await dbContext.SaveChangesAsync();
			_bidsheet = bidsheet;
		});
	
	async Task TheHtaContractIsRolled(string newFuturesMonth, decimal quantity) =>
		await Fixture.WithDbContextFor(_tenantData, async dbContext =>
		{
			var originalContract = await dbContext.Contracts
				.AsNoTracking()
				.OrderByDescending(x => x.CreatedOn)
				.FirstOrDefaultAsync(x => x.ContractTypeId == ContractTypeDictionary.HTA);
			originalContract.Should().NotBeNull();

			var contractRollDto = new ContractRollDto
			{
				DeliveryStartDate = originalContract.DeliveryStartDate,
				DeliveryEndDate = originalContract.DeliveryEndDate,
				FuturesMonth = newFuturesMonth,
				Quantity = quantity,
				FuturesPrice = 4.6025m,
				IsDeliveryDatesCustom = originalContract.IsDeliveryDatesCustom,
				EmployeeId = originalContract.EmployeeId,
				Comments = originalContract.Comments,
				CropYear = originalContract.CropYear,
				IsAppliedLoad = true,
				FreightPrice = originalContract.FreightPrice,
				Fees1 = originalContract.Fees1,
				Fees2 = originalContract.Fees2
			};

			var response = await Client.AuthenticatedAs(_fakeUser).PutAsync($"/api/contracts/roll/{originalContract.Id}", contractRollDto.ToJsonContent());
			response.IsSuccessStatusCode.Should().BeTrue();
		});
}
